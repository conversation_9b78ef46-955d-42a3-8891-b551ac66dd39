# Supabase Configuration for Testing
# Copy this file to .env.test and fill in your actual values

# Your Supabase project URL
SUPABASE_URL=https://your-project-ref.supabase.co

# Your Supabase anon key (public key)
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Your Supabase service role key (private key - keep this secure!)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Optional: Test database URL if using a separate test database
# TEST_DATABASE_URL=postgresql://user:password@localhost:5432/test_db
