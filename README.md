# Signup Flow with Supabase

A comprehensive signup flow built with Next.js and Supabase, featuring phone number verification, invitation system, and age verification.

## Features

- **Phone Number Input**: Users enter their phone number to start the signup process
- **Invitation System**: Users can only sign up if they're invited by existing users (up to 5 invites per user)
- **Waiting Room**: Non-invited users are added to a waiting room
- **OTP Verification**: Phone number verification via SMS (demo mode)
- **Age Verification**: Users must be 18+ to complete signup
- **Modern UI**: Beautiful, responsive design with Tailwind CSS
- **Edge Functions**: Backend logic handled by Supabase Edge Functions

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Edge Functions)
- **Forms**: React Hook Form
- **Notifications**: React Hot Toast
- **Styling**: Tailwind CSS

## Database Schema

### User Profile Table
- `id`: UUID (Primary Key)
- `phone_number`: VARCHAR(20) (Unique)
- `date_of_birth`: DATE
- `dob_verified`: BO<PERSON>EAN
- `created_at`: TIMESTAMP
- `updated_at`: TIMESTAMP

### Invites Table
- `id`: UUID (Primary Key)
- `inviter_id`: UUID (Foreign Key to users.id)
- `invitee_phone_number`: VARCHAR(20)
- `status`: VARCHAR(20) ('pending', 'accepted', 'expired')
- `created_at`: TIMESTAMP
- `accepted_at`: TIMESTAMP

### Waiting Room Table
- `id`: UUID (Primary Key)
- `phone_number`: VARCHAR(20) (Unique)
- `created_at`: TIMESTAMP

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd signup
npm install
```

### 2. Set up Supabase

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to your project dashboard
3. Navigate to SQL Editor and run the schema from `supabase/schema.sql`

### 3. Deploy Edge Functions

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Deploy edge functions
supabase functions deploy check-invite
supabase functions deploy add-to-waiting-room
supabase functions deploy complete-signup
```

### 4. Environment Variables

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# For API routes (server-side)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# For Edge Functions (service role)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 5. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Signup Flow

1. **Phone Number Input**: User enters their phone number
2. **Invitation Check**: System checks if the phone number is invited
3. **If Invited**: Proceed to OTP verification
4. **If Not Invited**: Add to waiting room
5. **OTP Verification**: User enters 6-digit code (demo: any 6 digits work)
6. **Date of Birth**: User provides their date of birth
7. **Age Verification**: System checks if user is 18+
8. **Success**: Account created and user can access the platform

## API Endpoints

### Frontend API Routes
- `POST /api/check-invite`: Check if phone number is invited
- `POST /api/add-to-waiting-room`: Add user to waiting room
- `POST /api/complete-signup`: Complete signup process

### Supabase Edge Functions
- `check-invite`: Check invitation status
- `add-to-waiting-room`: Add to waiting room
- `complete-signup`: Complete signup with age verification

## Testing the Flow

### For Invited Users:
1. Add a test invite in the database:
```sql
INSERT INTO invites (inviter_id, invitee_phone_number, status)
VALUES ('********-0000-0000-0000-********0000', '+**********', 'pending');
```

### For Non-Invited Users:
1. Enter any phone number not in the invites table
2. User will be added to waiting room

### Demo OTP:
- Any 6-digit code will be accepted for testing

## Production Considerations

1. **OTP Service**: Integrate with Twilio or similar service for real SMS
2. **Rate Limiting**: Add rate limiting to prevent abuse
3. **Security**: Implement proper authentication and authorization
4. **Monitoring**: Add logging and monitoring for edge functions
5. **Error Handling**: Improve error handling and user feedback

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
