# Backend Testing Guide

This guide covers the comprehensive backend testing setup for the Supabase signup flow.

## Overview

The testing suite includes:
- **Unit Tests**: Database functions, constraints, and business logic
- **Integration Tests**: Complete signup flow scenarios
- **Edge Function Simulation**: Tests that simulate your Supabase Edge Functions
- **Database Integration**: Real database operations with cleanup

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Up Environment

Copy the environment template and fill in your Supabase credentials:

```bash
cp .env.test.example .env.test
```

Edit `.env.test` with your actual Supabase values:
```env
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

### 3. Set Up Test Database

```bash
npm run setup:test-db
```

This will:
- Verify your Supabase connection
- Check that all required tables exist
- Verify database functions are available
- Clean up any existing test data

### 4. Run Tests

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit        # Unit tests only
npm run test:integration # Integration tests only

# Development
npm run test:watch       # Watch mode for development
npm run test:coverage    # Generate coverage report
```

## Test Structure

```
tests/
├── setup.ts                    # Global test configuration
├── unit/
│   └── database-functions.test.ts  # Database function tests
└── integration/
    ├── signup-flow.test.ts         # End-to-end signup scenarios
    └── edge-functions.test.ts      # Edge function simulation tests
```

## Test Categories

### Unit Tests (`tests/unit/`)

**Database Functions**
- `can_invite_more()` function validation
- `get_available_invites()` function validation
- Database constraints (unique phone numbers, etc.)
- Index performance verification

### Integration Tests (`tests/integration/`)

**Signup Flow Tests**
- Complete invited user signup flow
- Non-invited user waiting room flow
- Age verification scenarios
- Duplicate prevention

**Edge Function Simulation**
- `check-invite` function logic
- `add-to-waiting-room` function logic
- `complete-signup` function logic
- Error handling and validation

## Test Data Management

### Automatic Cleanup
- Tests use phone numbers starting with `+1555` for easy identification
- Global setup/teardown handles cleanup automatically
- Each test creates and cleans its own data

### Test Helpers
```typescript
// Generate unique test phone numbers
const phoneNumber = generateTestPhoneNumber();
const phoneNumber2 = generateTestPhoneNumber('suffix');

// Create test dates
const adultBirthDate = createTestDate(25);  // 25 years ago
const minorBirthDate = createTestDate(16);  // 16 years ago
```

## Running Specific Tests

```bash
# Run tests matching a pattern
npm test -- --testNamePattern="should complete full signup"

# Run tests in a specific file
npm test -- tests/unit/database-functions.test.ts

# Run with verbose output
npm test -- --verbose

# Run with debugging
npm test -- --detectOpenHandles --forceExit
```

## Test Scenarios Covered

### ✅ Invited User Flow
- Phone number has pending invite
- OTP verification (simulated)
- Age verification (18+ required)
- User profile creation
- Invite marked as accepted
- Removal from waiting room (if applicable)

### ✅ Non-Invited User Flow
- Phone number not in invites
- Addition to waiting room
- Duplicate prevention
- Waiting room status check

### ✅ Edge Cases
- Underage users (< 18)
- Duplicate phone numbers
- Invalid input validation
- Database constraint violations
- Existing user updates

### ✅ Database Functions
- Invite limit enforcement (5 per user)
- Available invite counting
- Constraint validation
- Index performance

## Troubleshooting

### Common Issues

**Connection Errors**
```
❌ Failed to connect to Supabase
```
- Check your `.env.test` file has correct values
- Verify your Supabase project is running
- Ensure service role key has proper permissions

**Missing Tables**
```
❌ Table 'user_profile' missing
```
- Run the `supabase/schema.sql` in your Supabase SQL editor
- Ensure all tables and functions are created

**Permission Errors**
```
❌ RLS policy violation
```
- Tests use service role key to bypass RLS
- Verify your service role key is correct

**Test Timeouts**
```
❌ Test timeout after 30000ms
```
- Check your internet connection
- Verify Supabase project is responsive
- Consider increasing timeout in jest config

### Manual Cleanup

If tests leave behind data:

```bash
npm run clean:test-db
```

Or manually clean specific tables:
```sql
DELETE FROM invites WHERE invitee_phone_number LIKE '+1555%';
DELETE FROM waiting_room WHERE phone_number LIKE '+1555%';
DELETE FROM user_profile WHERE phone_number LIKE '+1555%';
```

## Best Practices

### Writing New Tests
1. Use `generateTestPhoneNumber()` for unique phone numbers
2. Clean up test data in `afterEach` or `afterAll`
3. Use descriptive test names
4. Test both success and failure scenarios
5. Verify database state changes

### Test Data
- Always use `+1555` prefix for test phone numbers
- Use `createTestDate()` for consistent date generation
- Create minimal test data needed for each test
- Don't rely on data from other tests

### Performance
- Use `beforeAll`/`afterAll` for expensive setup
- Use `beforeEach`/`afterEach` for test isolation
- Consider using database transactions for faster cleanup
- Run tests in parallel when possible

## Continuous Integration

For CI/CD pipelines, ensure:
1. Supabase credentials are available as environment variables
2. Database schema is applied before tests
3. Tests run with appropriate timeouts
4. Test results are properly reported

Example GitHub Actions:
```yaml
- name: Run Backend Tests
  env:
    SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
    SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
  run: |
    npm run setup:test-db
    npm test
```

## Next Steps

1. **Run the tests** to verify your signup flow works correctly
2. **Add more test cases** for specific business requirements
3. **Set up CI/CD** to run tests automatically
4. **Monitor test coverage** to ensure comprehensive testing
5. **Consider load testing** for production readiness
