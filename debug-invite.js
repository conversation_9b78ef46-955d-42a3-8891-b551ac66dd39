// Debug invite status
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NjE2NTIyNywiZXhwIjoyMDcxNzQxMjI3fQ.L9sCBfB4SShmb8Z83oPMWyhJIogTKQQ-uDSlbdxN2ms';

const TEST_PHONE = '+14167090286';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function debugInviteStatus() {
  console.log('🔍 Debugging invite status for:', TEST_PHONE);
  
  // Check all invites for this phone number
  const { data: allInvites, error: allError } = await supabase
    .from('invites')
    .select('*')
    .eq('invitee_phone_number', TEST_PHONE);
  
  console.log('📊 All invites for this phone:', allInvites);
  console.log('❌ Error:', allError);
  
  // Check pending invites specifically
  const { data: pendingInvites, error: pendingError } = await supabase
    .from('invites')
    .select('*')
    .eq('invitee_phone_number', TEST_PHONE)
    .eq('status', 'pending');
  
  console.log('📨 Pending invites:', pendingInvites);
  console.log('❌ Pending error:', pendingError);
  
  // Check if user exists
  const { data: existingUser, error: userError } = await supabase
    .from('user_profile')
    .select('*')
    .eq('phone_number', TEST_PHONE);
  
  console.log('👤 Existing users:', existingUser);
  console.log('❌ User error:', userError);
  
  // Create a fresh invite if none exists
  if (!allInvites || allInvites.length === 0) {
    console.log('📨 Creating fresh invite...');
    
    const { data: newInvite, error: createError } = await supabase
      .from('invites')
      .insert({
        inviter_id: '00000000-0000-0000-0000-000000000000',
        invitee_phone_number: TEST_PHONE,
        status: 'pending',
      })
      .select('*')
      .single();
    
    console.log('✅ New invite created:', newInvite);
    console.log('❌ Create error:', createError);
  } else if (allInvites[0].status !== 'pending') {
    console.log('🔄 Resetting invite to pending...');
    
    const { data: updatedInvite, error: updateError } = await supabase
      .from('invites')
      .update({ status: 'pending', accepted_at: null })
      .eq('id', allInvites[0].id)
      .select('*')
      .single();
    
    console.log('✅ Invite updated:', updatedInvite);
    console.log('❌ Update error:', updateError);
  }
  
  // Final check
  const { data: finalCheck, error: finalError } = await supabase
    .from('invites')
    .select('*')
    .eq('invitee_phone_number', TEST_PHONE)
    .eq('status', 'pending')
    .single();
  
  console.log('🎯 Final pending invite check:', finalCheck);
  console.log('❌ Final error:', finalError);
}

debugInviteStatus().catch(console.error);
