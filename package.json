{"name": "signup-flow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "node test-suite.js"}, "dependencies": {"next": "14.0.0", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.0", "@supabase/auth-helpers-nextjs": "^0.8.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8"}}