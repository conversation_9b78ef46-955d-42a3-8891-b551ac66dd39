{"name": "signup-backend-tests", "version": "1.0.0", "description": "Backend testing for Supabase signup flow", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:e2e": "jest --testPathPattern=e2e", "test:real-phone": "jest --testPathPattern=real-phone-test", "test:otp": "jest --testPathPattern=otp-integration", "setup:test-db": "node scripts/setup-test-db.js", "clean:test-db": "node scripts/clean-test-db.js", "test:local-functions": "node scripts/test-local-functions.js"}, "keywords": ["supabase", "testing", "signup", "backend"], "author": "", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "dotenv": "^16.3.1", "node-fetch": "^2.7.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["supabase/functions/**/*.ts", "!supabase/functions/**/*.d.ts"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "testTimeout": 30000}}