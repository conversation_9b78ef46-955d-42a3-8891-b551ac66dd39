#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env.test');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function cleanTestDatabase() {
  console.log('🧹 Cleaning test database...');

  try {
    // Clean up test data in reverse order of dependencies
    console.log('🗑️  Removing test invites...');
    const { error: invitesError } = await supabase
      .from('invites')
      .delete()
      .like('invitee_phone_number', '+1555%');

    if (invitesError) {
      console.error('❌ Error cleaning invites:', invitesError.message);
    } else {
      console.log('✅ Test invites cleaned');
    }

    console.log('🗑️  Removing test waiting room entries...');
    const { error: waitingError } = await supabase
      .from('waiting_room')
      .delete()
      .like('phone_number', '+1555%');

    if (waitingError) {
      console.error('❌ Error cleaning waiting room:', waitingError.message);
    } else {
      console.log('✅ Test waiting room entries cleaned');
    }

    console.log('🗑️  Removing test user profiles...');
    const { error: usersError } = await supabase
      .from('user_profile')
      .delete()
      .like('phone_number', '+1555%');

    if (usersError) {
      console.error('❌ Error cleaning user profiles:', usersError.message);
    } else {
      console.log('✅ Test user profiles cleaned');
    }

    console.log('');
    console.log('🎉 Test database cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error cleaning test database:', error.message);
    process.exit(1);
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanTestDatabase();
}

module.exports = { cleanTestDatabase };
