#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const REAL_PHONE_NUMBER = '+14167090286';

async function resetInviteAndUser() {
  console.log('🔄 Resetting invite and user for', REAL_PHONE_NUMBER);

  try {
    // 1. Delete any existing user profile
    const { error: profileError } = await supabase
      .from('user_profile')
      .delete()
      .eq('phone_number', REAL_PHONE_NUMBER);

    if (profileError) {
      console.log('⚠️ Error deleting user profile:', profileError.message);
    } else {
      console.log('✅ User profile deleted');
    }

    // 2. Delete any auth users with this phone number
    try {
      const { data: existingUsers } = await supabase.auth.admin.listUsers();
      const userToDelete = existingUsers.users.find(user => 
        user.phone === REAL_PHONE_NUMBER || 
        user.user_metadata?.phone_number === REAL_PHONE_NUMBER ||
        user.email === `${REAL_PHONE_NUMBER}@temp.com`
      );
      
      if (userToDelete) {
        await supabase.auth.admin.deleteUser(userToDelete.id);
        console.log('✅ Auth user deleted:', userToDelete.id);
      } else {
        console.log('ℹ️ No auth user found to delete');
      }
    } catch (error) {
      console.log('⚠️ Could not clean up auth users:', error.message);
    }

    // 3. Reset invite to pending status
    const { data: invite, error: inviteError } = await supabase
      .from('invites')
      .update({
        status: 'pending',
        accepted_at: null,
      })
      .eq('invitee_phone_number', REAL_PHONE_NUMBER)
      .select('*')
      .single();

    if (inviteError) {
      console.log('⚠️ Error resetting invite:', inviteError.message);
    } else {
      console.log('✅ Invite reset to pending:', invite);
    }

    // 4. Remove from waiting room if present
    const { error: waitingError } = await supabase
      .from('waiting_room')
      .delete()
      .eq('phone_number', REAL_PHONE_NUMBER);

    if (waitingError) {
      console.log('⚠️ Error removing from waiting room:', waitingError.message);
    } else {
      console.log('✅ Removed from waiting room (if present)');
    }

    console.log('');
    console.log('🎉 Reset completed! Ready for fresh signup test.');
    console.log('');
    console.log('📱 Phone number:', REAL_PHONE_NUMBER);
    console.log('📨 Invite status: pending');
    console.log('👤 User profile: deleted');
    console.log('🔐 Auth user: deleted');
    console.log('');
    console.log('💡 Now you can run:');
    console.log('   npm run test:local-functions');
    console.log('   npm run test:e2e');

  } catch (error) {
    console.error('❌ Error during reset:', error.message);
    process.exit(1);
  }
}

// Run reset if this script is executed directly
if (require.main === module) {
  resetInviteAndUser();
}

module.exports = { resetInviteAndUser };
