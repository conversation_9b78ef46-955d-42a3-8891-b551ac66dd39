#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env.test');
  console.error('   Copy .env.test.example to .env.test and fill in your values');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function setupTestDatabase() {
  console.log('🚀 Setting up test database...');

  try {
    // Test connection
    console.log('📡 Testing Supabase connection...');
    const { data, error } = await supabase.from('user_profile').select('count').limit(1);
    
    if (error) {
      console.error('❌ Failed to connect to Supabase:', error.message);
      console.error('   Please check your SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
      process.exit(1);
    }

    console.log('✅ Supabase connection successful');

    // Check if required tables exist
    console.log('🔍 Checking database schema...');
    
    const tables = ['user_profile', 'invites', 'waiting_room'];
    const missingTables = [];

    for (const table of tables) {
      try {
        await supabase.from(table).select('count').limit(1);
        console.log(`✅ Table '${table}' exists`);
      } catch (error) {
        missingTables.push(table);
        console.log(`❌ Table '${table}' missing`);
      }
    }

    if (missingTables.length > 0) {
      console.error('❌ Missing required tables:', missingTables.join(', '));
      console.error('   Please run the schema.sql file in your Supabase SQL editor');
      process.exit(1);
    }

    // Check if required functions exist
    console.log('🔍 Checking database functions...');
    
    try {
      // Test can_invite_more function
      const { error: funcError1 } = await supabase.rpc('can_invite_more', { 
        inviter_uuid: '00000000-0000-0000-0000-000000000000' 
      });
      
      if (funcError1 && !funcError1.message.includes('does not exist')) {
        console.log('✅ Function can_invite_more exists');
      } else if (funcError1) {
        console.log('❌ Function can_invite_more missing');
      }

      // Test get_available_invites function
      const { error: funcError2 } = await supabase.rpc('get_available_invites', { 
        user_uuid: '00000000-0000-0000-0000-000000000000' 
      });
      
      if (funcError2 && !funcError2.message.includes('does not exist')) {
        console.log('✅ Function get_available_invites exists');
      } else if (funcError2) {
        console.log('❌ Function get_available_invites missing');
      }

    } catch (error) {
      console.log('⚠️  Could not verify database functions');
    }

    // Clean up any existing test data
    console.log('🧹 Cleaning up existing test data...');
    
    await supabase.from('invites').delete().like('invitee_phone_number', '+1555%');
    await supabase.from('waiting_room').delete().like('phone_number', '+1555%');
    await supabase.from('user_profile').delete().like('phone_number', '+1555%');

    console.log('✅ Test data cleanup completed');

    console.log('');
    console.log('🎉 Test database setup completed successfully!');
    console.log('');
    console.log('You can now run tests with:');
    console.log('  npm test                 # Run all tests');
    console.log('  npm run test:unit        # Run unit tests only');
    console.log('  npm run test:integration # Run integration tests only');
    console.log('  npm run test:watch       # Run tests in watch mode');
    console.log('  npm run test:coverage    # Run tests with coverage report');

  } catch (error) {
    console.error('❌ Error setting up test database:', error.message);
    process.exit(1);
  }
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupTestDatabase();
}

module.exports = { setupTestDatabase };
