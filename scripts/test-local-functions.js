#!/usr/bin/env node

const fetch = require('node-fetch');
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

const LOCAL_FUNCTIONS_URL = 'http://localhost:54321/functions/v1';
const REAL_PHONE_NUMBER = '+14167090286';

// Helper function to call edge functions
async function callEdgeFunction(functionName, payload) {
  try {
    console.log(`📡 Calling ${functionName} with:`, payload);
    
    const response = await fetch(`${LOCAL_FUNCTIONS_URL}/${functionName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    const result = { status: response.status, data };
    
    console.log(`📋 ${functionName} response:`, result);
    return result;
    
  } catch (error) {
    console.error(`❌ Error calling ${functionName}:`, error.message);
    return { status: 500, error: error.message };
  }
}

async function testLocalFunctions() {
  console.log('🚀 Testing Local Supabase Edge Functions');
  console.log('==========================================');
  console.log('');

  // Check if local functions are running
  try {
    const healthCheck = await fetch(`${LOCAL_FUNCTIONS_URL}/check-invite`, {
      method: 'OPTIONS',
    });
    
    if (!healthCheck.ok) {
      throw new Error('Functions not responding');
    }
    
    console.log('✅ Local Supabase functions are running');
  } catch (error) {
    console.error('❌ Local Supabase functions are not running');
    console.error('   Please start them with: supabase functions serve');
    process.exit(1);
  }

  console.log('');
  console.log('📱 Testing with phone number:', REAL_PHONE_NUMBER);
  console.log('');

  // Test 1: Check invite
  console.log('🔍 Test 1: Check Invite Status');
  console.log('------------------------------');
  const checkResult = await callEdgeFunction('check-invite', {
    phone_number: REAL_PHONE_NUMBER
  });
  
  if (checkResult.status === 200) {
    console.log('✅ Check invite successful');
    if (checkResult.data.is_invited) {
      console.log('📨 User is invited');
      console.log('   Invite ID:', checkResult.data.invite_id);
      console.log('   Existing user:', checkResult.data.is_existing_user);
    } else {
      console.log('⏳ User not invited - would go to waiting room');
    }
  } else {
    console.log('❌ Check invite failed');
  }

  console.log('');

  // Test 2: Complete signup (if invited)
  if (checkResult.data?.is_invited && !checkResult.data?.is_existing_user) {
    console.log('🎯 Test 2: Complete Signup');
    console.log('---------------------------');
    
    const birthDate = new Date();
    birthDate.setFullYear(birthDate.getFullYear() - 25);
    const dateOfBirth = birthDate.toISOString().split('T')[0];
    
    const signupResult = await callEdgeFunction('complete-signup', {
      phone_number: REAL_PHONE_NUMBER,
      date_of_birth: dateOfBirth,
      invite_id: checkResult.data.invite_id
    });
    
    if (signupResult.status === 201) {
      console.log('✅ Signup completed successfully');
      console.log('👤 User created:', signupResult.data.user);
    } else {
      console.log('❌ Signup failed:', signupResult.data);
    }
  } else if (checkResult.data?.is_existing_user) {
    console.log('👤 User already exists - skipping signup test');
  } else {
    console.log('📨 User not invited - testing waiting room');
    
    console.log('');
    console.log('⏳ Test 2: Add to Waiting Room');
    console.log('-------------------------------');
    
    const waitingResult = await callEdgeFunction('add-to-waiting-room', {
      phone_number: REAL_PHONE_NUMBER
    });
    
    if (waitingResult.status === 201 || waitingResult.status === 200) {
      console.log('✅ Added to waiting room successfully');
      console.log('🆔 Waiting room ID:', waitingResult.data.waiting_room_id);
    } else {
      console.log('❌ Failed to add to waiting room:', waitingResult.data);
    }
  }

  console.log('');
  console.log('🎉 Local function testing completed!');
  console.log('');
  console.log('💡 Next steps:');
  console.log('   1. Run the full E2E tests: npm test -- tests/e2e/');
  console.log('   2. Test OTP integration: npm test -- tests/e2e/otp-integration.test.ts');
  console.log('   3. Check your Supabase dashboard for created users');
}

// Run the test if this script is executed directly
if (require.main === module) {
  testLocalFunctions().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testLocalFunctions, callEdgeFunction };
