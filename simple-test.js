// Simple Test Script for Tunnel Environment
import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_ANON_KEY = 'sb_publishable_dkeH98OqCJZZ2oQkbkiWzQ_zUINGKUo';
const SUPABASE_SERVICE_KEY = 'sb_secret_U62_cB4ChAjb0dnK0fI-Eg_ae3jg0R2';

// Test data
const TEST_PHONE = '+14167090286';
const TEST_DOB = '1990-01-01';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const log = (message, data = null) => {
  console.log(`\n${'='.repeat(50)}`);
  console.log(`🧪 ${message}`);
  if (data) {
    console.log('📊 Result:', JSON.stringify(data, null, 2));
  }
  console.log(`${'='.repeat(50)}`);
};

async function runSimpleTests() {
  console.log('🚀 Running Simple Signup Flow Tests...\n');

  try {
    // Test 1: Connection
    log('Test 1: Testing Supabase Connection');
    const { data, error } = await supabase
      .from('user_profile')
      .select('count')
      .limit(1);
    
    if (error) {
      log('❌ Connection failed', { error: error.message });
      return;
    }
    log('✅ Connection successful', { data });

    // Test 2: Check tables exist
    log('Test 2: Checking if tables exist');
    const tables = ['user_profile', 'invites', 'waiting_room'];
    
    for (const table of tables) {
      const { data: tableData, error: tableError } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      log(`${table} table check`, { 
        exists: !tableError, 
        data: tableData, 
        error: tableError 
      });
    }

    // Test 3: Check invite for test phone
    log('Test 3: Checking invite for test phone');
    const { data: invite, error: inviteError } = await supabase
      .from('invites')
      .select('*')
      .eq('invitee_phone_number', TEST_PHONE)
      .eq('status', 'pending')
      .single();
    
    log('Invite check', { invite, error: inviteError });

    // Test 4: Check if user exists
    log('Test 4: Checking if user exists');
    const { data: user, error: userError } = await supabase
      .from('user_profile')
      .select('*')
      .eq('phone_number', TEST_PHONE)
      .single();
    
    log('User check', { user, error: userError });

    // Test 5: Age verification logic
    log('Test 5: Testing age verification logic');
    const birthDate = new Date(TEST_DOB);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    log('Age calculation', { 
      birthDate: TEST_DOB, 
      calculatedAge: age, 
      isAdult: age >= 18 
    });

    // Test 6: Test Edge Functions (if deployed)
    log('Test 6: Testing Edge Functions');
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/check-invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        },
        body: JSON.stringify({ phone_number: TEST_PHONE }),
      });

      const result = await response.json();
      log('Edge function test', { 
        status: response.status, 
        deployed: response.status !== 404,
        result 
      });
    } catch (error) {
      log('Edge function not deployed or error', { error: error.message });
    }

    console.log('\n🎉 Simple tests completed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Database connection working');
    console.log('- ✅ Tables accessible');
    console.log('- ✅ Age verification logic working');
    console.log('- ⚠️  Edge Functions may need deployment');

  } catch (error) {
    log('❌ Test error', { error: error.message });
  }
}

runSimpleTests().catch(console.error);
