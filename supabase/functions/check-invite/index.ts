import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone_number } = await req.json()

    if (!phone_number) {
      return new Response(
        JSON.stringify({ error: 'Phone number is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client with service role for admin operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Check if phone number exists in user_profile table
    const { data: existingUser, error: userError } = await supabaseClient
      .from('user_profile')
      .select('id, phone_number')
      .eq('phone_number', phone_number)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      throw userError
    }

    if (existingUser) {
      return new Response(
        JSON.stringify({ 
          is_invited: true, 
          is_existing_user: true,
          user_id: existingUser.id 
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if phone number is in invites table
    const { data: invite, error: inviteError } = await supabaseClient
      .from('invites')
      .select('id, inviter_id, status')
      .eq('invitee_phone_number', phone_number)
      .eq('status', 'pending')
      .single()

    if (inviteError && inviteError.code !== 'PGRST116') {
      throw inviteError
    }

    if (invite) {
      return new Response(
        JSON.stringify({ 
          is_invited: true, 
          is_existing_user: false,
          invite_id: invite.id,
          inviter_id: invite.inviter_id
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if phone number is in waiting room
    const { data: waitingRoom, error: waitingError } = await supabaseClient
      .from('waiting_room')
      .select('id')
      .eq('phone_number', phone_number)
      .single()

    if (waitingError && waitingError.code !== 'PGRST116') {
      throw waitingError
    }

    if (waitingRoom) {
      return new Response(
        JSON.stringify({ 
          is_invited: false, 
          is_existing_user: false,
          is_in_waiting_room: true,
          waiting_room_id: waitingRoom.id
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Phone number is not invited and not in waiting room
    return new Response(
      JSON.stringify({ 
        is_invited: false, 
        is_existing_user: false,
        is_in_waiting_room: false
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
