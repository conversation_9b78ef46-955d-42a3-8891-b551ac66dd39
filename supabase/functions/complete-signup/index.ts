import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone_number, date_of_birth, invite_id } = await req.json()

    if (!phone_number || !date_of_birth) {
      return new Response(
        JSON.stringify({ error: 'Phone number and date of birth are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client with service role for admin operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Calculate age
    const birthDate = new Date(date_of_birth)
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    if (age < 18) {
      return new Response(
        JSON.stringify({ 
          error: 'You must be at least 18 years old to sign up',
          age: age
        }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if user already exists
    const { data: existingUser, error: userError } = await supabaseClient
      .from('user_profile')
      .select('id, phone_number')
      .eq('phone_number', phone_number)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      throw userError
    }

    if (existingUser) {
      // Update existing user with DOB
      const { data: updatedUser, error: updateError } = await supabaseClient
        .from('user_profile')
        .update({ 
          date_of_birth: date_of_birth,
          dob_verified: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select('id, phone_number, date_of_birth, dob_verified')
        .single()

      if (updateError) {
        throw updateError
      }

      return new Response(
        JSON.stringify({ 
          message: 'User updated successfully',
          user: updatedUser
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase Auth user first
    const { data: authUser, error: authError } = await supabaseClient.auth.admin.createUser({
      email: `${phone_number}@temp.com`, // Temporary email for auth
      phone: phone_number,
      email_confirm: true,
      user_metadata: {
        phone_number: phone_number
      }
    })

    if (authError) {
      throw authError
    }

    // Create user profile linked to auth user
    const { data: newUser, error: insertError } = await supabaseClient
      .from('user_profile')
      .insert([{ 
        id: authUser.user.id, // Use auth user ID
        phone_number,
        date_of_birth,
        dob_verified: true
      }])
      .select('id, phone_number, date_of_birth, dob_verified')
      .single()

    if (insertError) {
      throw insertError
    }

    // If there was an invite, mark it as accepted
    if (invite_id) {
      const { error: inviteUpdateError } = await supabaseClient
        .from('invites')
        .update({ 
          status: 'accepted',
          accepted_at: new Date().toISOString()
        })
        .eq('id', invite_id)

      if (inviteUpdateError) {
        console.error('Error updating invite:', inviteUpdateError)
        // Don't fail the signup if invite update fails
      }
    }

    // Remove from waiting room if they were there
    const { error: waitingRoomError } = await supabaseClient
      .from('waiting_room')
      .delete()
      .eq('phone_number', phone_number)

    if (waitingRoomError) {
      console.error('Error removing from waiting room:', waitingRoomError)
      // Don't fail the signup if waiting room removal fails
    }

    return new Response(
      JSON.stringify({ 
        message: 'Signup completed successfully',
        user: newUser
      }),
      { 
        status: 201, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
