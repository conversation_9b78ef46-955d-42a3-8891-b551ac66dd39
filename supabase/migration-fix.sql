-- Migration to fix foreign key constraint
-- Run this in your Supabase SQL Editor

-- Drop the existing foreign key constraint
ALTER TABLE invites DROP CONSTRAINT IF EXISTS invites_inviter_id_fkey;

-- Add the correct foreign key constraint
ALTER TABLE invites ADD CONSTRAINT invites_inviter_id_fkey 
  FOREIGN KEY (inviter_id) REFERENCES user_profile(id) ON DELETE CASCADE;

-- Create a test user profile for testing
INSERT INTO user_profile (id, phone_number, date_of_birth, dob_verified)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '+1111111111',
  '1990-01-01',
  true
) ON CONFLICT (id) DO NOTHING;

-- Create a test invite
INSERT INTO invites (inviter_id, invitee_phone_number, status)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '+1234567890',
  'pending'
) ON CONFLICT (inviter_id, invitee_phone_number) DO NOTHING;

