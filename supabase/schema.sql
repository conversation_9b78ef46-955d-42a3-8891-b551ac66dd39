-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_profile table
CREATE TABLE user_profile (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  phone_number VARCHAR(20) UNIQUE NOT NULL,
  date_of_birth DATE,
  dob_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> invites table to track user invitations
CREATE TABLE invites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  inviter_id UUID REFERENCES user_profile(id) ON DELETE CASCADE,
  invitee_phone_number VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, expired
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(inviter_id, invitee_phone_number)
);

-- Create waiting_room table for non-invited users
CREATE TABLE waiting_room (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  phone_number VARCHAR(20) UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to check if user can invite more people
CREATE OR REPLACE FUNCTION can_invite_more(inviter_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  invite_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO invite_count
  FROM invites
  WHERE inviter_id = inviter_uuid AND status = 'accepted';
  
  RETURN invite_count < 5;
END;
$$ LANGUAGE plpgsql;

-- Create function to get available invites count
CREATE OR REPLACE FUNCTION get_available_invites(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  used_invites INTEGER;
BEGIN
  SELECT COUNT(*) INTO used_invites
  FROM invites
  WHERE inviter_id = user_uuid AND status = 'accepted';
  
  RETURN 5 - used_invites;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX idx_user_profile_phone ON user_profile(phone_number);
CREATE INDEX idx_invites_inviter ON invites(inviter_id);
CREATE INDEX idx_invites_phone ON invites(invitee_phone_number);
CREATE INDEX idx_waiting_room_phone ON waiting_room(phone_number);

-- Row Level Security (RLS) policies
ALTER TABLE user_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;
ALTER TABLE waiting_room ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can view own data" ON user_profile
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own data" ON user_profile
  FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own data
CREATE POLICY "Users can insert own data" ON user_profile
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow service role to insert/update (for edge functions)
CREATE POLICY "Service role can manage user_profile" ON user_profile
  FOR ALL USING (auth.role() = 'service_role');

-- Invites policies
CREATE POLICY "Users can view their invites" ON invites
  FOR SELECT USING (auth.uid()::text = inviter_id::text);

CREATE POLICY "Users can create invites" ON invites
  FOR INSERT WITH CHECK (auth.uid()::text = inviter_id::text);

CREATE POLICY "Users can update their invites" ON invites
  FOR UPDATE USING (auth.uid()::text = inviter_id::text);

-- Waiting room policies (public read, service role insert)
CREATE POLICY "Anyone can view waiting room" ON waiting_room
  FOR SELECT USING (true);

CREATE POLICY "Service role can add to waiting room" ON waiting_room
  FOR INSERT WITH CHECK (auth.role() = 'service_role');
