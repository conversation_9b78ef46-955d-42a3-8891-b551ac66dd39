// Test the complete-signup function directly
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxNjUyMjcsImV4cCI6MjA3MTc0MTIyN30.z6BgIJhXB2sy8bEkzc96OJBJHQiwH8MSuRnEcMzdULQ';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NjE2NTIyNywiZXhwIjoyMDcxNzQxMjI3fQ.L9sCBfB4SShmb8Z83oPMWyhJIogTKQQ-uDSlbdxN2ms';

const TEST_PHONE = '+14167090286';
const INVITE_ID = 'a2c2e8a0-25df-47e9-83b7-36cc5c057e13';

const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testCompleteSignup() {
  console.log('🎯 Testing complete-signup function directly');
  console.log('📱 Phone:', TEST_PHONE);
  console.log('📨 Invite ID:', INVITE_ID);
  
  // Clean up any existing user first
  console.log('🧹 Cleaning up existing user...');
  await supabaseService.from('user_profile').delete().eq('phone_number', TEST_PHONE);
  
  // Clean up any auth users
  try {
    const { data: existingUsers } = await supabaseService.auth.admin.listUsers();
    const userToDelete = existingUsers.users.find(user => 
      user.phone === TEST_PHONE || 
      user.phone === TEST_PHONE.replace('+', '') ||
      user.user_metadata?.phone_number === TEST_PHONE ||
      user.email === `${TEST_PHONE}@temp.com`
    );
    
    if (userToDelete) {
      await supabaseService.auth.admin.deleteUser(userToDelete.id);
      console.log('🗑️ Cleaned up existing auth user');
    }
  } catch (error) {
    console.log('⚠️ Could not clean up auth users');
  }
  
  console.log('✅ Cleanup complete');
  
  // Test the complete-signup function
  console.log('\n🚀 Calling complete-signup function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/complete-signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        phone_number: TEST_PHONE,
        date_of_birth: '1990-01-01',
        invite_id: INVITE_ID
      }),
    });

    const result = await response.json();
    
    console.log('📊 Complete-signup result:', {
      status: response.status,
      success: response.ok,
      result
    });
    
    if (response.ok) {
      console.log('🎉 SIGNUP SUCCESSFUL!');
      console.log('👤 User created:', result.user);
      
      // Verify user in Supabase Auth
      console.log('\n🔐 Verifying user in Supabase Auth...');
      const { data: authUser, error: authError } = await supabaseService.auth.admin.getUserById(result.user.id);
      
      if (authError) {
        console.log('❌ Auth verification error:', authError);
      } else {
        console.log('✅ User verified in Auth:', {
          id: authUser.user.id,
          phone: authUser.user.phone,
          email: authUser.user.email,
          created_at: authUser.user.created_at
        });
      }
      
      // Verify user in user_profile table
      console.log('\n📊 Verifying user in user_profile table...');
      const { data: userProfile, error: profileError } = await supabaseService
        .from('user_profile')
        .select('*')
        .eq('id', result.user.id)
        .single();
      
      if (profileError) {
        console.log('❌ Profile verification error:', profileError);
      } else {
        console.log('✅ User verified in profile table:', userProfile);
      }
      
      // Verify invite was marked as accepted
      console.log('\n📨 Verifying invite status...');
      const { data: invite, error: inviteError } = await supabaseService
        .from('invites')
        .select('*')
        .eq('id', INVITE_ID)
        .single();
      
      if (inviteError) {
        console.log('❌ Invite verification error:', inviteError);
      } else {
        console.log('✅ Invite status:', {
          status: invite.status,
          accepted_at: invite.accepted_at
        });
      }
      
      console.log('\n🎉 COMPLETE END-TO-END SIGNUP FLOW SUCCESSFUL!');
      console.log('✅ User created in both Supabase Auth and user_profile table');
      console.log('✅ Invite marked as accepted');
      console.log('✅ All database relationships working correctly');
      
    } else {
      console.log('❌ SIGNUP FAILED:', result);
    }
    
  } catch (error) {
    console.error('❌ Error calling complete-signup:', error.message);
  }
}

testCompleteSignup().catch(console.error);
