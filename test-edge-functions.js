// Quick test for edge functions with correct authentication
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxNjUyMjcsImV4cCI6MjA3MTc0MTIyN30.z6BgIJhXB2sy8bEkzc96OJBJHQiwH8MSuRnEcMzdULQ';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NjE2NTIyNywiZXhwIjoyMDcxNzQxMjI3fQ.L9sCBfB4SShmb8Z83oPMWyhJIogTKQQ-uDSlbdxN2ms';

const TEST_PHONE = '+14167090286';

const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function setupTestData() {
  console.log('🔧 Setting up test data...');
  
  // Clean up existing data
  await supabaseService.from('user_profile').delete().eq('phone_number', TEST_PHONE);
  await supabaseService.from('waiting_room').delete().eq('phone_number', TEST_PHONE);
  
  // Ensure we have a pending invite
  const { data: existingInvite } = await supabaseService
    .from('invites')
    .select('id, status')
    .eq('invitee_phone_number', TEST_PHONE)
    .single();

  if (existingInvite) {
    // Reset to pending
    await supabaseService
      .from('invites')
      .update({ status: 'pending', accepted_at: null })
      .eq('id', existingInvite.id);
    console.log('✅ Reset existing invite to pending');
  } else {
    // Create new invite
    const { data: newInvite } = await supabaseService
      .from('invites')
      .insert({
        inviter_id: '00000000-0000-0000-0000-000000000000',
        invitee_phone_number: TEST_PHONE,
        status: 'pending',
      })
      .select('id')
      .single();
    
    console.log('✅ Created new invite:', newInvite.id);
  }
}

async function testEdgeFunction(functionName, payload) {
  console.log(`\n🧪 Testing ${functionName}...`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`, // Using anon key
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();
    
    console.log(`📊 ${functionName} result:`, {
      status: response.status,
      success: response.ok,
      result
    });
    
    return { success: response.ok, status: response.status, result };
  } catch (error) {
    console.error(`❌ ${functionName} error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function testCompleteSignupFlow() {
  console.log('🚀 Testing Complete Signup Flow with Fixed Edge Function\n');
  
  // Setup
  await setupTestData();
  
  // Test 1: Check invite
  const checkResult = await testEdgeFunction('check-invite', {
    phone_number: TEST_PHONE
  });
  
  if (!checkResult.success) {
    console.log('❌ Cannot proceed without working check-invite function');
    return;
  }
  
  // Test 2: Complete signup (if invited)
  if (checkResult.result.is_invited && !checkResult.result.is_existing_user) {
    console.log('✅ User is invited, proceeding with signup...');
    
    const signupResult = await testEdgeFunction('complete-signup', {
      phone_number: TEST_PHONE,
      date_of_birth: '1990-01-01',
      invite_id: checkResult.result.invite_id
    });
    
    if (signupResult.success) {
      console.log('🎉 SIGNUP SUCCESSFUL!');
      console.log('👤 User created:', signupResult.result.user);
      
      // Verify user in database
      const { data: userProfile } = await supabaseService
        .from('user_profile')
        .select('*')
        .eq('phone_number', TEST_PHONE)
        .single();
      
      console.log('📊 User in database:', userProfile);
      
      // Verify invite was accepted
      const { data: invite } = await supabaseService
        .from('invites')
        .select('status, accepted_at')
        .eq('id', checkResult.result.invite_id)
        .single();
      
      console.log('📨 Invite status:', invite);
      
    } else {
      console.log('❌ Signup failed:', signupResult);
    }
  } else if (checkResult.result.is_existing_user) {
    console.log('👤 User already exists');
  } else {
    console.log('📨 User not invited');
  }
  
  // Test 3: Test waiting room function
  console.log('\n🧪 Testing waiting room function...');
  const waitingResult = await testEdgeFunction('add-to-waiting-room', {
    phone_number: '+15551234567' // Different phone number
  });
  
  console.log('\n🎉 Edge Function Testing Complete!');
  console.log('📋 Summary:');
  console.log('  - check-invite:', checkResult.success ? '✅' : '❌');
  console.log('  - complete-signup:', signupResult?.success ? '✅' : '❌');
  console.log('  - add-to-waiting-room:', waitingResult.success ? '✅' : '❌');
}

// Run the test
testCompleteSignupFlow().catch(console.error);
