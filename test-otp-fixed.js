// Test OTP with correct phone number format
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxNjUyMjcsImV4cCI6MjA3MTc0MTIyN30.z6BgIJhXB2sy8bEkzc96OJBJHQiwH8MSuRnEcMzdULQ';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpteG9pYWVmeGZueHlyeHJlcGN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NjE2NTIyNywiZXhwIjoyMDcxNzQxMjI3fQ.L9sCBfB4SShmb8Z83oPMWyhJIogTKQQ-uDSlbdxN2ms';

const TEST_PHONE_WITH_PLUS = '+***********';
const TEST_PHONE_WITHOUT_PLUS = '***********';

const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testOTPFormats() {
  console.log('📱 Testing OTP with different phone number formats');
  console.log('==================================================\n');

  // Test 1: OTP with + prefix
  console.log('🧪 Test 1: Sending OTP with + prefix:', TEST_PHONE_WITH_PLUS);
  try {
    const { data: data1, error: error1 } = await supabaseAnon.auth.signInWithOtp({
      phone: TEST_PHONE_WITH_PLUS,
    });

    console.log('📊 Result with +:', { data: data1, error: error1?.message });
    
    if (!error1) {
      console.log('✅ OTP sent successfully with + prefix!');
      console.log('📱 Check your phone for OTP');
    } else {
      console.log('❌ Failed with + prefix:', error1.message);
    }
  } catch (error) {
    console.log('❌ Exception with + prefix:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Wait a bit to avoid rate limiting
  console.log('⏳ Waiting 5 seconds to avoid rate limiting...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Test 2: OTP without + prefix
  console.log('🧪 Test 2: Sending OTP without + prefix:', TEST_PHONE_WITHOUT_PLUS);
  try {
    const { data: data2, error: error2 } = await supabaseAnon.auth.signInWithOtp({
      phone: TEST_PHONE_WITHOUT_PLUS,
    });

    console.log('📊 Result without +:', { data: data2, error: error2?.message });
    
    if (!error2) {
      console.log('✅ OTP sent successfully without + prefix!');
      console.log('📱 Check your phone for OTP');
    } else {
      console.log('❌ Failed without + prefix:', error2.message);
    }
  } catch (error) {
    console.log('❌ Exception without + prefix:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Check current SMS configuration
  console.log('🔧 Test 3: Checking SMS configuration...');
  
  // Try to get project settings (this might not work with anon key)
  try {
    const { data: settings, error: settingsError } = await supabaseService.auth.admin.listUsers();
    
    if (settingsError) {
      console.log('⚠️ Cannot check SMS settings:', settingsError.message);
    } else {
      console.log('✅ Auth admin access working');
    }
  } catch (error) {
    console.log('⚠️ Cannot access auth admin:', error.message);
  }

  console.log('\n📋 Summary:');
  console.log('===========');
  console.log('💡 If neither format worked, you need to:');
  console.log('   1. Go to Supabase Dashboard > Authentication > Settings');
  console.log('   2. Scroll to "SMS" section');
  console.log('   3. Configure a SMS provider (Twilio recommended)');
  console.log('   4. Add your Twilio Account SID and Auth Token');
  console.log('   5. Add a Twilio phone number');
  console.log('');
  console.log('💡 If one format worked, use that format consistently');
  console.log('');
  console.log('🔍 Phone number format findings:');
  console.log('   - Database stores: +***********');
  console.log('   - Auth might expect: *********** (without +)');
  console.log('   - Need to handle both formats in edge functions');
}

async function testCompleteSignupWithFixedFormat() {
  console.log('\n🎯 Testing complete signup with fixed phone format');
  console.log('=================================================\n');

  // Clean up first
  await supabaseService.from('user_profile').delete().eq('phone_number', TEST_PHONE_WITH_PLUS);
  
  try {
    const { data: existingUsers } = await supabaseService.auth.admin.listUsers();
    const userToDelete = existingUsers.users.find(user => 
      user.phone === TEST_PHONE_WITHOUT_PLUS || 
      user.phone === TEST_PHONE_WITH_PLUS ||
      user.user_metadata?.phone_number === TEST_PHONE_WITH_PLUS ||
      user.email === `${TEST_PHONE_WITH_PLUS}@temp.com`
    );
    
    if (userToDelete) {
      await supabaseService.auth.admin.deleteUser(userToDelete.id);
      console.log('🗑️ Cleaned up existing auth user');
    }
  } catch (error) {
    console.log('⚠️ Could not clean up auth users');
  }

  // Reset invite
  await supabaseService
    .from('invites')
    .update({ status: 'pending', accepted_at: null })
    .eq('invitee_phone_number', TEST_PHONE_WITH_PLUS);

  console.log('✅ Cleanup complete');

  // Test the fixed complete-signup function
  console.log('🚀 Testing fixed complete-signup function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/complete-signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        phone_number: TEST_PHONE_WITH_PLUS,
        date_of_birth: '1990-01-01',
        invite_id: 'a2c2e8a0-25df-47e9-83b7-36cc5c057e13'
      }),
    });

    const result = await response.json();
    
    console.log('📊 Complete-signup result:', {
      status: response.status,
      success: response.ok,
      result
    });
    
    if (response.ok) {
      console.log('🎉 SIGNUP SUCCESSFUL WITH FIXED FORMAT!');
      
      // Check the auth user phone format
      const { data: authUser } = await supabaseService.auth.admin.getUserById(result.user.id);
      console.log('📱 Auth user phone format:', authUser.user.phone);
      console.log('📧 Auth user email:', authUser.user.email);
      
      // Now test OTP with the auth user's phone format
      console.log('\n📱 Testing OTP with auth user phone format...');
      
      const { data: otpData, error: otpError } = await supabaseAnon.auth.signInWithOtp({
        phone: authUser.user.phone, // Use the format stored in auth
      });
      
      if (otpError) {
        console.log('❌ OTP failed:', otpError.message);
      } else {
        console.log('✅ OTP sent successfully!');
        console.log('📱 Check your phone for OTP');
      }
      
    } else {
      console.log('❌ Signup failed:', result);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function runTests() {
  await testOTPFormats();
  await testCompleteSignupWithFixedFormat();
}

runTests().catch(console.error);
