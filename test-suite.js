// Comprehensive Test Suite for Signup Flow
import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://zmxoiaefxfnxyrxrepcy.supabase.co';
const SUPABASE_ANON_KEY = 'sb_publishable_dkeH98OqCJZZ2oQkbkiWzQ_zUINGKUo';
const SUPABASE_SERVICE_KEY = 'sb_secret_U62_cB4ChAjb0dnK0fI-Eg_ae3jg0R2';

// Test data
const TEST_PHONE_INVITED = '+14167090286';
const TEST_PHONE_NOT_INVITED = '+9999999999';
const TEST_DOB_VALID = '1990-01-01';
const TEST_DOB_UNDERAGE = '2010-01-01';

// Create Supabase clients
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test utilities
const log = (message, data = null) => {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🧪 ${message}`);
  if (data) {
    console.log('📊 Result:', JSON.stringify(data, null, 2));
  }
  console.log(`${'='.repeat(60)}`);
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test categories
class DatabaseTests {
  static async testConnection() {
    log('Testing Supabase Connection');
    try {
      const { data, error } = await supabaseAnon
        .from('user_profile')
        .select('count')
        .limit(1);
      
      if (error) {
        log('❌ Connection failed', { error: error.message });
        return false;
      }
      log('✅ Connection successful', { data });
      return true;
    } catch (error) {
      log('❌ Connection error', { error: error.message });
      return false;
    }
  }

  static async testTableAccess() {
    log('Testing Table Access');
    const tables = ['user_profile', 'invites', 'waiting_room'];
    const results = {};

    for (const table of tables) {
      try {
        const { data, error } = await supabaseAnon
          .from(table)
          .select('*')
          .limit(1);
        
        results[table] = { success: !error, data, error };
      } catch (error) {
        results[table] = { success: false, error: error.message };
      }
    }

    log('Table access results', results);
    return results;
  }

  static async testInviteFlow() {
    log('Testing Invite Flow');
    
    // 1. Check if invited phone exists
    const { data: invite, error: inviteError } = await supabaseAnon
      .from('invites')
      .select('*')
      .eq('invitee_phone_number', TEST_PHONE_INVITED)
      .eq('status', 'pending')
      .single();
    
    log('Invite check', { invite, error: inviteError });

    // 2. Check if user already exists
    const { data: existingUser, error: userError } = await supabaseAnon
      .from('user_profile')
      .select('*')
      .eq('phone_number', TEST_PHONE_INVITED)
      .single();
    
    log('Existing user check', { existingUser, error: userError });

    return { invite, existingUser, inviteError, userError };
  }

  static async testWaitingRoomFlow() {
    log('Testing Waiting Room Flow');
    
    // 1. Check if phone is in waiting room
    const { data: waitingRoom, error: waitingError } = await supabaseAnon
      .from('waiting_room')
      .select('*')
      .eq('phone_number', TEST_PHONE_NOT_INVITED)
      .single();
    
    log('Waiting room check', { waitingRoom, error: waitingError });

    // 2. Add to waiting room
    const { data: newWaiting, error: addError } = await supabaseService
      .from('waiting_room')
      .insert([{ phone_number: TEST_PHONE_NOT_INVITED }])
      .select()
      .single();
    
    log('Add to waiting room', { newWaiting, error: addError });

    return { waitingRoom, newWaiting, waitingError, addError };
  }

  static async testUserCreation() {
    log('Testing User Creation');
    
    // 1. Create user with service role
    const { data: newUser, error: createError } = await supabaseService
      .from('user_profile')
      .insert([{
        phone_number: TEST_PHONE_INVITED,
        date_of_birth: TEST_DOB_VALID,
        dob_verified: true
      }])
      .select()
      .single();
    
    log('User creation', { newUser, error: createError });

    // 2. Verify user was created
    const { data: verifyUser, error: verifyError } = await supabaseAnon
      .from('user_profile')
      .select('*')
      .eq('phone_number', TEST_PHONE_INVITED)
      .single();
    
    log('User verification', { verifyUser, error: verifyError });

    return { newUser, verifyUser, createError, verifyError };
  }

  static async testAgeVerification() {
    log('Testing Age Verification');
    
    const birthDate = new Date(TEST_DOB_UNDERAGE);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    log('Age calculation', { 
      birthDate: TEST_DOB_UNDERAGE, 
      calculatedAge: age, 
      isUnderage: age < 18 
    });

    return { age, isUnderage: age < 18 };
  }

  static async cleanupTestData() {
    log('Cleaning up test data');
    
    try {
      // Clean up test users
      await supabaseService
        .from('user_profile')
        .delete()
        .eq('phone_number', TEST_PHONE_INVITED);
      
      // Clean up waiting room
      await supabaseService
        .from('waiting_room')
        .delete()
        .eq('phone_number', TEST_PHONE_NOT_INVITED);
      
      log('✅ Cleanup completed');
    } catch (error) {
      log('❌ Cleanup error', { error: error.message });
    }
  }
}

class EdgeFunctionTests {
  static async testCheckInviteFunction() {
    log('Testing Check Invite Edge Function');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/check-invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        },
        body: JSON.stringify({ phone_number: TEST_PHONE_INVITED }),
      });

      const result = await response.json();
      log('Check invite function', { 
        status: response.status, 
        result 
      });
      
      return { success: response.ok, status: response.status, result };
    } catch (error) {
      log('❌ Check invite function error', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  static async testAddToWaitingRoomFunction() {
    log('Testing Add to Waiting Room Edge Function');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/add-to-waiting-room`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        },
        body: JSON.stringify({ phone_number: TEST_PHONE_NOT_INVITED }),
      });

      const result = await response.json();
      log('Add to waiting room function', { 
        status: response.status, 
        result 
      });
      
      return { success: response.ok, status: response.status, result };
    } catch (error) {
      log('❌ Add to waiting room function error', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  static async testCompleteSignupFunction() {
    log('Testing Complete Signup Edge Function');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/complete-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        },
        body: JSON.stringify({
          phone_number: TEST_PHONE_INVITED,
          date_of_birth: TEST_DOB_VALID,
        }),
      });

      const result = await response.json();
      log('Complete signup function', { 
        status: response.status, 
        result 
      });
      
      return { success: response.ok, status: response.status, result };
    } catch (error) {
      log('❌ Complete signup function error', { error: error.message });
      return { success: false, error: error.message };
    }
  }
}

class IntegrationTests {
  static async testCompleteSignupFlow() {
    log('Testing Complete Signup Flow Integration');
    
    const results = {
      step1: await DatabaseTests.testInviteFlow(),
      step2: await DatabaseTests.testWaitingRoomFlow(),
      step3: await DatabaseTests.testUserCreation(),
      step4: await DatabaseTests.testAgeVerification(),
    };

    log('Complete flow results', results);
    return results;
  }

  static async testErrorScenarios() {
    log('Testing Error Scenarios');
    
    // Test underage signup
    try {
      const { data, error } = await supabaseService
        .from('user_profile')
        .insert([{
          phone_number: '+1234567890',
          date_of_birth: TEST_DOB_UNDERAGE,
          dob_verified: false
        }])
        .select()
        .single();
      
      log('Underage signup attempt', { data, error });
    } catch (error) {
      log('Underage signup error (expected)', { error: error.message });
    }

    // Test duplicate phone number
    try {
      const { data, error } = await supabaseService
        .from('user_profile')
        .insert([{
          phone_number: TEST_PHONE_INVITED,
          date_of_birth: TEST_DOB_VALID,
          dob_verified: true
        }])
        .select()
        .single();
      
      log('Duplicate phone attempt', { data, error });
    } catch (error) {
      log('Duplicate phone error (expected)', { error: error.message });
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Signup Flow Test Suite\n');
  
  const results = {
    connection: false,
    database: {},
    edgeFunctions: {},
    integration: {},
  };

  // Test 1: Connection
  results.connection = await DatabaseTests.testConnection();
  if (!results.connection) {
    log('❌ Cannot proceed without database connection');
    return results;
  }

  await sleep(1000);

  // Test 2: Database operations
  results.database = {
    tableAccess: await DatabaseTests.testTableAccess(),
    inviteFlow: await DatabaseTests.testInviteFlow(),
    waitingRoomFlow: await DatabaseTests.testWaitingRoomFlow(),
    userCreation: await DatabaseTests.testUserCreation(),
    ageVerification: await DatabaseTests.testAgeVerification(),
  };

  await sleep(1000);

  // Test 3: Edge Functions
  results.edgeFunctions = {
    checkInvite: await EdgeFunctionTests.testCheckInviteFunction(),
    addToWaitingRoom: await EdgeFunctionTests.testAddToWaitingRoomFunction(),
    completeSignup: await EdgeFunctionTests.testCompleteSignupFunction(),
  };

  await sleep(1000);

  // Test 4: Integration
  results.integration = {
    completeFlow: await IntegrationTests.testCompleteSignupFlow(),
  };

  await sleep(1000);

  // Test 5: Error scenarios
  await IntegrationTests.testErrorScenarios();

  await sleep(1000);

  // Cleanup
  await DatabaseTests.cleanupTestData();

  // Summary
  log('🎉 Test Suite Completed!');
  log('📋 Summary', results);

  return results;
}

// Run tests
runAllTests().catch(console.error);
