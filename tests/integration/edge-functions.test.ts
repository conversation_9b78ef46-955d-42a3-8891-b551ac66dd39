import { supabase, generateTestPhoneNumber, createTestDate } from '../setup';

// Helper function to simulate edge function logic
class EdgeFunctionSimulator {
  static async checkInvite(phoneNumber: string) {
    try {
      if (!phoneNumber) {
        return {
          status: 400,
          data: { error: 'Phone number is required' }
        };
      }

      // Check if phone number exists in user_profile table
      const { data: existingUser, error: userError } = await supabase
        .from('user_profile')
        .select('id, phone_number')
        .eq('phone_number', phoneNumber)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        throw userError;
      }

      if (existingUser) {
        return {
          status: 200,
          data: {
            is_invited: true,
            is_existing_user: true,
            user_id: existingUser.id
          }
        };
      }

      // Check if phone number is in invites table
      const { data: invite, error: inviteError } = await supabase
        .from('invites')
        .select('id, inviter_id, status')
        .eq('invitee_phone_number', phoneNumber)
        .eq('status', 'pending')
        .single();

      if (inviteError && inviteError.code !== 'PGRST116') {
        throw inviteError;
      }

      if (invite) {
        return {
          status: 200,
          data: {
            is_invited: true,
            is_existing_user: false,
            invite_id: invite.id,
            inviter_id: invite.inviter_id
          }
        };
      }

      // Check if phone number is in waiting room
      const { data: waitingRoom, error: waitingError } = await supabase
        .from('waiting_room')
        .select('id')
        .eq('phone_number', phoneNumber)
        .single();

      if (waitingError && waitingError.code !== 'PGRST116') {
        throw waitingError;
      }

      if (waitingRoom) {
        return {
          status: 200,
          data: {
            is_invited: false,
            is_existing_user: false,
            is_in_waiting_room: true,
            waiting_room_id: waitingRoom.id
          }
        };
      }

      return {
        status: 200,
        data: {
          is_invited: false,
          is_existing_user: false,
          is_in_waiting_room: false
        }
      };

    } catch (error) {
      return {
        status: 500,
        data: { error: 'Internal server error' }
      };
    }
  }

  static async addToWaitingRoom(phoneNumber: string) {
    try {
      if (!phoneNumber) {
        return {
          status: 400,
          data: { error: 'Phone number is required' }
        };
      }

      // Check if phone number already exists in waiting room
      const { data: existingEntry, error: checkError } = await supabase
        .from('waiting_room')
        .select('id')
        .eq('phone_number', phoneNumber)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingEntry) {
        return {
          status: 200,
          data: {
            message: 'Phone number already in waiting room',
            waiting_room_id: existingEntry.id
          }
        };
      }

      // Add to waiting room
      const { data: newEntry, error: insertError } = await supabase
        .from('waiting_room')
        .insert([{ phone_number: phoneNumber }])
        .select('id')
        .single();

      if (insertError) {
        throw insertError;
      }

      return {
        status: 201,
        data: {
          message: 'Successfully added to waiting room',
          waiting_room_id: newEntry.id
        }
      };

    } catch (error) {
      return {
        status: 500,
        data: { error: 'Internal server error' }
      };
    }
  }

  static async completeSignup(phoneNumber: string, dateOfBirth: string, inviteId?: string) {
    try {
      if (!phoneNumber || !dateOfBirth) {
        return {
          status: 400,
          data: { error: 'Phone number and date of birth are required' }
        };
      }

      // Calculate age
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      if (age < 18) {
        return {
          status: 403,
          data: {
            error: 'You must be at least 18 years old to sign up',
            age: age
          }
        };
      }

      // Check if user already exists
      const { data: existingUser, error: userError } = await supabase
        .from('user_profile')
        .select('id, phone_number')
        .eq('phone_number', phoneNumber)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        throw userError;
      }

      if (existingUser) {
        // Update existing user with DOB
        const { data: updatedUser, error: updateError } = await supabase
          .from('user_profile')
          .update({
            date_of_birth: dateOfBirth,
            dob_verified: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingUser.id)
          .select('id, phone_number, date_of_birth, dob_verified')
          .single();

        if (updateError) {
          throw updateError;
        }

        return {
          status: 200,
          data: {
            message: 'User updated successfully',
            user: updatedUser
          }
        };
      }

      // Create user profile (simplified - no auth user creation in tests)
      const { data: newUser, error: insertError } = await supabase
        .from('user_profile')
        .insert([{
          phone_number,
          date_of_birth: dateOfBirth,
          dob_verified: true
        }])
        .select('id, phone_number, date_of_birth, dob_verified')
        .single();

      if (insertError) {
        throw insertError;
      }

      // If there was an invite, mark it as accepted
      if (inviteId) {
        await supabase
          .from('invites')
          .update({
            status: 'accepted',
            accepted_at: new Date().toISOString()
          })
          .eq('id', inviteId);
      }

      // Remove from waiting room if they were there
      await supabase
        .from('waiting_room')
        .delete()
        .eq('phone_number', phoneNumber);

      return {
        status: 201,
        data: {
          message: 'Signup completed successfully',
          user: newUser
        }
      };

    } catch (error) {
      return {
        status: 500,
        data: { error: 'Internal server error' }
      };
    }
  }
}

describe('Edge Functions Simulation Tests', () => {
  describe('check-invite function', () => {
    let testPhoneNumber: string;
    let inviterUserId: string;
    let inviteId: string;

    beforeEach(async () => {
      testPhoneNumber = generateTestPhoneNumber();
      
      // Create inviter user
      const { data: inviterUser, error: inviterError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: generateTestPhoneNumber('inv'),
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(inviterError).toBeNull();
      inviterUserId = inviterUser.id;
    });

    afterEach(async () => {
      await supabase.from('invites').delete().eq('inviter_id', inviterUserId);
      await supabase.from('user_profile').delete().eq('phone_number', testPhoneNumber);
      await supabase.from('user_profile').delete().eq('id', inviterUserId);
      await supabase.from('waiting_room').delete().eq('phone_number', testPhoneNumber);
    });

    test('should return error for missing phone number', async () => {
      const result = await EdgeFunctionSimulator.checkInvite('');
      
      expect(result.status).toBe(400);
      expect(result.data.error).toBe('Phone number is required');
    });

    test('should identify existing user', async () => {
      // Create existing user
      await supabase
        .from('user_profile')
        .insert({
          phone_number: testPhoneNumber,
          date_of_birth: createTestDate(25),
          dob_verified: true,
        });

      const result = await EdgeFunctionSimulator.checkInvite(testPhoneNumber);
      
      expect(result.status).toBe(200);
      expect(result.data.is_invited).toBe(true);
      expect(result.data.is_existing_user).toBe(true);
      expect(result.data.user_id).toBeTruthy();
    });

    test('should identify invited user', async () => {
      // Create invite
      const { data: invite } = await supabase
        .from('invites')
        .insert({
          inviter_id: inviterUserId,
          invitee_phone_number: testPhoneNumber,
          status: 'pending',
        })
        .select()
        .single();

      const result = await EdgeFunctionSimulator.checkInvite(testPhoneNumber);
      
      expect(result.status).toBe(200);
      expect(result.data.is_invited).toBe(true);
      expect(result.data.is_existing_user).toBe(false);
      expect(result.data.invite_id).toBe(invite.id);
      expect(result.data.inviter_id).toBe(inviterUserId);
    });

    test('should identify user in waiting room', async () => {
      // Add to waiting room
      await supabase
        .from('waiting_room')
        .insert({ phone_number: testPhoneNumber });

      const result = await EdgeFunctionSimulator.checkInvite(testPhoneNumber);
      
      expect(result.status).toBe(200);
      expect(result.data.is_invited).toBe(false);
      expect(result.data.is_existing_user).toBe(false);
      expect(result.data.is_in_waiting_room).toBe(true);
      expect(result.data.waiting_room_id).toBeTruthy();
    });

    test('should identify new user', async () => {
      const result = await EdgeFunctionSimulator.checkInvite(testPhoneNumber);
      
      expect(result.status).toBe(200);
      expect(result.data.is_invited).toBe(false);
      expect(result.data.is_existing_user).toBe(false);
      expect(result.data.is_in_waiting_room).toBe(false);
    });
  });

  describe('add-to-waiting-room function', () => {
    let testPhoneNumber: string;

    beforeEach(() => {
      testPhoneNumber = generateTestPhoneNumber();
    });

    afterEach(async () => {
      await supabase.from('waiting_room').delete().eq('phone_number', testPhoneNumber);
    });

    test('should return error for missing phone number', async () => {
      const result = await EdgeFunctionSimulator.addToWaitingRoom('');
      
      expect(result.status).toBe(400);
      expect(result.data.error).toBe('Phone number is required');
    });

    test('should add new user to waiting room', async () => {
      const result = await EdgeFunctionSimulator.addToWaitingRoom(testPhoneNumber);
      
      expect(result.status).toBe(201);
      expect(result.data.message).toBe('Successfully added to waiting room');
      expect(result.data.waiting_room_id).toBeTruthy();

      // Verify user was added
      const { data: waitingUser } = await supabase
        .from('waiting_room')
        .select('phone_number')
        .eq('phone_number', testPhoneNumber)
        .single();

      expect(waitingUser.phone_number).toBe(testPhoneNumber);
    });

    test('should handle duplicate waiting room entries', async () => {
      // Add user first time
      await EdgeFunctionSimulator.addToWaitingRoom(testPhoneNumber);
      
      // Try to add same user again
      const result = await EdgeFunctionSimulator.addToWaitingRoom(testPhoneNumber);
      
      expect(result.status).toBe(200);
      expect(result.data.message).toBe('Phone number already in waiting room');
      expect(result.data.waiting_room_id).toBeTruthy();
    });
  });

  describe('complete-signup function', () => {
    let testPhoneNumber: string;

    beforeEach(() => {
      testPhoneNumber = generateTestPhoneNumber();
    });

    afterEach(async () => {
      await supabase.from('user_profile').delete().eq('phone_number', testPhoneNumber);
      await supabase.from('waiting_room').delete().eq('phone_number', testPhoneNumber);
    });

    test('should return error for missing required fields', async () => {
      const result1 = await EdgeFunctionSimulator.completeSignup('', createTestDate(25));
      expect(result1.status).toBe(400);
      expect(result1.data.error).toBe('Phone number and date of birth are required');

      const result2 = await EdgeFunctionSimulator.completeSignup(testPhoneNumber, '');
      expect(result2.status).toBe(400);
      expect(result2.data.error).toBe('Phone number and date of birth are required');
    });

    test('should reject underage users', async () => {
      const underageBirthDate = createTestDate(16);
      
      const result = await EdgeFunctionSimulator.completeSignup(testPhoneNumber, underageBirthDate);
      
      expect(result.status).toBe(403);
      expect(result.data.error).toBe('You must be at least 18 years old to sign up');
      expect(result.data.age).toBeLessThan(18);
    });

    test('should complete signup for valid user', async () => {
      const validBirthDate = createTestDate(25);
      
      const result = await EdgeFunctionSimulator.completeSignup(testPhoneNumber, validBirthDate);
      
      expect(result.status).toBe(201);
      expect(result.data.message).toBe('Signup completed successfully');
      expect(result.data.user.phone_number).toBe(testPhoneNumber);
      expect(result.data.user.dob_verified).toBe(true);
    });

    test('should update existing user', async () => {
      // Create existing user without DOB
      const { data: existingUser } = await supabase
        .from('user_profile')
        .insert({
          phone_number: testPhoneNumber,
          dob_verified: false,
        })
        .select()
        .single();

      const validBirthDate = createTestDate(25);
      const result = await EdgeFunctionSimulator.completeSignup(testPhoneNumber, validBirthDate);
      
      expect(result.status).toBe(200);
      expect(result.data.message).toBe('User updated successfully');
      expect(result.data.user.dob_verified).toBe(true);
      expect(result.data.user.date_of_birth).toBe(validBirthDate);
    });
  });
});
