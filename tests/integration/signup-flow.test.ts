import { supabase, generateTestPhoneNumber, createTestDate } from '../setup';

describe('Signup Flow Integration Tests', () => {
  describe('Complete Signup Flow - Invited User', () => {
    let testPhoneNumber: string;
    let inviteId: string;
    let inviterUserId: string;

    beforeEach(async () => {
      testPhoneNumber = generateTestPhoneNumber();
      
      // Create a test inviter user
      const { data: inviterUser, error: inviterError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: generateTestPhoneNumber('inv'),
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(inviterError).toBeNull();
      inviterUserId = inviterUser.id;

      // Create an invite for our test phone number
      const { data: invite, error: inviteError } = await supabase
        .from('invites')
        .insert({
          inviter_id: inviterUserId,
          invitee_phone_number: testPhoneNumber,
          status: 'pending',
        })
        .select()
        .single();

      expect(inviteError).toBeNull();
      inviteId = invite.id;
    });

    afterEach(async () => {
      // Clean up test data
      await supabase.from('invites').delete().eq('id', inviteId);
      await supabase.from('user_profile').delete().eq('phone_number', testPhoneNumber);
      await supabase.from('user_profile').delete().eq('id', inviterUserId);
    });

    test('should complete full signup flow for invited user', async () => {
      // Step 1: Check invite status
      const { data: inviteCheck, error: checkError } = await supabase
        .from('invites')
        .select('id, inviter_id, status')
        .eq('invitee_phone_number', testPhoneNumber)
        .eq('status', 'pending')
        .single();

      expect(checkError).toBeNull();
      expect(inviteCheck).toBeTruthy();
      expect(inviteCheck.status).toBe('pending');

      // Step 2: Complete signup with valid age
      const validBirthDate = createTestDate(25); // 25 years old
      
      const { data: newUser, error: signupError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: testPhoneNumber,
          date_of_birth: validBirthDate,
          dob_verified: true,
        })
        .select()
        .single();

      expect(signupError).toBeNull();
      expect(newUser).toBeTruthy();
      expect(newUser.phone_number).toBe(testPhoneNumber);
      expect(newUser.dob_verified).toBe(true);

      // Step 3: Mark invite as accepted
      const { error: inviteUpdateError } = await supabase
        .from('invites')
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
        })
        .eq('id', inviteId);

      expect(inviteUpdateError).toBeNull();

      // Step 4: Verify invite was marked as accepted
      const { data: updatedInvite, error: verifyError } = await supabase
        .from('invites')
        .select('status, accepted_at')
        .eq('id', inviteId)
        .single();

      expect(verifyError).toBeNull();
      expect(updatedInvite.status).toBe('accepted');
      expect(updatedInvite.accepted_at).toBeTruthy();
    });

    test('should reject signup for underage user', async () => {
      const underageBirthDate = createTestDate(16); // 16 years old
      
      // Calculate age to verify it's under 18
      const birthDate = new Date(underageBirthDate);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      expect(age).toBeLessThan(18);

      // Attempt to create user profile (this would normally be rejected by the edge function)
      // For this test, we'll simulate the age check logic
      if (age < 18) {
        // This simulates the edge function rejecting the signup
        expect(age).toBeLessThan(18);
        return;
      }

      // If we reach here, the test should fail
      fail('Underage user should not be able to complete signup');
    });
  });

  describe('Complete Signup Flow - Non-Invited User', () => {
    let testPhoneNumber: string;

    beforeEach(() => {
      testPhoneNumber = generateTestPhoneNumber();
    });

    afterEach(async () => {
      // Clean up test data
      await supabase.from('waiting_room').delete().eq('phone_number', testPhoneNumber);
    });

    test('should add non-invited user to waiting room', async () => {
      // Step 1: Verify user is not invited
      const { data: inviteCheck, error: checkError } = await supabase
        .from('invites')
        .select('id')
        .eq('invitee_phone_number', testPhoneNumber)
        .eq('status', 'pending')
        .single();

      expect(checkError?.code).toBe('PGRST116'); // No rows found
      expect(inviteCheck).toBeNull();

      // Step 2: Add to waiting room
      const { data: waitingRoomEntry, error: waitingError } = await supabase
        .from('waiting_room')
        .insert({
          phone_number: testPhoneNumber,
        })
        .select()
        .single();

      expect(waitingError).toBeNull();
      expect(waitingRoomEntry).toBeTruthy();
      expect(waitingRoomEntry.phone_number).toBe(testPhoneNumber);

      // Step 3: Verify user is in waiting room
      const { data: verifyWaiting, error: verifyError } = await supabase
        .from('waiting_room')
        .select('id, phone_number, created_at')
        .eq('phone_number', testPhoneNumber)
        .single();

      expect(verifyError).toBeNull();
      expect(verifyWaiting).toBeTruthy();
      expect(verifyWaiting.phone_number).toBe(testPhoneNumber);
    });

    test('should not add duplicate entries to waiting room', async () => {
      // Add user to waiting room first time
      const { data: firstEntry, error: firstError } = await supabase
        .from('waiting_room')
        .insert({
          phone_number: testPhoneNumber,
        })
        .select()
        .single();

      expect(firstError).toBeNull();
      expect(firstEntry).toBeTruthy();

      // Attempt to add same user again
      const { data: secondEntry, error: secondError } = await supabase
        .from('waiting_room')
        .insert({
          phone_number: testPhoneNumber,
        })
        .select()
        .single();

      // Should fail due to unique constraint
      expect(secondError).toBeTruthy();
      expect(secondError?.code).toBe('23505'); // Unique violation
      expect(secondEntry).toBeNull();
    });
  });
});
