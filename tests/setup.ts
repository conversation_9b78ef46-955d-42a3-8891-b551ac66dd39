import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.test' });

// Global test configuration
export const testConfig = {
  supabaseUrl: process.env.SUPABASE_URL || '',
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  supabaseAnonKey: process.env.SUPABASE_ANON_KEY || '',
  testTimeout: 30000,
};

// Create Supabase client for tests
export const supabase = createClient(
  testConfig.supabaseUrl,
  testConfig.supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Test data cleanup function
export const cleanupTestData = async () => {
  try {
    // Clean up test data in reverse order of dependencies
    await supabase.from('invites').delete().like('invitee_phone_number', '+1555%');
    await supabase.from('waiting_room').delete().like('phone_number', '+1555%');
    await supabase.from('user_profile').delete().like('phone_number', '+1555%');
    
    console.log('Test data cleaned up successfully');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
};

// Setup test data
export const setupTestData = async () => {
  try {
    // Create a test user for invitations
    const { data: testUser, error: userError } = await supabase
      .from('user_profile')
      .insert({
        phone_number: '+15551234567',
        date_of_birth: '1990-01-01',
        dob_verified: true,
      })
      .select()
      .single();

    if (userError) throw userError;

    // Create some test invites
    const { error: inviteError } = await supabase
      .from('invites')
      .insert([
        {
          inviter_id: testUser.id,
          invitee_phone_number: '+15559876543',
          status: 'pending',
        },
        {
          inviter_id: testUser.id,
          invitee_phone_number: '+15559876544',
          status: 'accepted',
        },
      ]);

    if (inviteError) throw inviteError;

    console.log('Test data setup completed');
    return { testUser };
  } catch (error) {
    console.error('Error setting up test data:', error);
    throw error;
  }
};

// Global setup and teardown
beforeAll(async () => {
  await cleanupTestData();
  await setupTestData();
});

afterAll(async () => {
  await cleanupTestData();
});

// Helper function to generate test phone numbers
export const generateTestPhoneNumber = (suffix: string = '') => {
  const timestamp = Date.now().toString().slice(-6);
  return `+1555${timestamp}${suffix}`;
};

// Helper function to create test dates
export const createTestDate = (yearsAgo: number) => {
  const date = new Date();
  date.setFullYear(date.getFullYear() - yearsAgo);
  return date.toISOString().split('T')[0];
};
