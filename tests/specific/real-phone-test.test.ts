import { supabase, createTestDate } from '../setup';

describe('Real Phone Number Test - +14167090286', () => {
  const realPhoneNumber = '+14167090286';

  beforeAll(async () => {
    // Clean up any existing user profile for this phone number
    await supabase.from('user_profile').delete().eq('phone_number', realPhoneNumber);
    await supabase.from('waiting_room').delete().eq('phone_number', realPhoneNumber);
  });

  afterAll(async () => {
    // Clean up after tests
    await supabase.from('user_profile').delete().eq('phone_number', realPhoneNumber);
    await supabase.from('waiting_room').delete().eq('phone_number', realPhoneNumber);
  });

  test('should find existing invite for +14167090286', async () => {
    console.log('🔍 Checking invite status for', realPhoneNumber);

    // Check if phone number exists in invites table
    const { data: invite, error: inviteError } = await supabase
      .from('invites')
      .select('id, inviter_id, status, created_at, invitee_phone_number')
      .eq('invitee_phone_number', realPhoneNumber)
      .single();

    console.log('📋 Invite query result:', { invite, error: inviteError });

    if (inviteError) {
      if (inviteError.code === 'PGRST116') {
        console.log('❌ No invite found for this phone number');
        console.log('💡 You may need to add an invite to the database first');
        fail('No invite found for +14167090286. Please add an invite to the invites table first.');
      } else {
        console.error('❌ Database error:', inviteError);
        throw inviteError;
      }
    }

    expect(invite).toBeTruthy();
    expect(invite.invitee_phone_number).toBe(realPhoneNumber);
    console.log('✅ Found invite:', invite);
  });

  test('should simulate check-invite edge function for +14167090286', async () => {
    console.log('🧪 Simulating check-invite function for', realPhoneNumber);

    // Step 1: Check if user already exists
    const { data: existingUser, error: userError } = await supabase
      .from('user_profile')
      .select('id, phone_number')
      .eq('phone_number', realPhoneNumber)
      .single();

    if (userError && userError.code !== 'PGRST116') {
      throw userError;
    }

    if (existingUser) {
      console.log('👤 User already exists:', existingUser);
      expect(existingUser.phone_number).toBe(realPhoneNumber);
      return;
    }

    // Step 2: Check for pending invite
    const { data: invite, error: inviteError } = await supabase
      .from('invites')
      .select('id, inviter_id, status, invitee_phone_number')
      .eq('invitee_phone_number', realPhoneNumber)
      .eq('status', 'pending')
      .single();

    if (inviteError && inviteError.code !== 'PGRST116') {
      throw inviteError;
    }

    if (invite) {
      console.log('📨 Found pending invite:', invite);
      expect(invite.status).toBe('pending');
      expect(invite.invitee_phone_number).toBe(realPhoneNumber);
      return;
    }

    // Step 3: Check waiting room
    const { data: waitingRoom, error: waitingError } = await supabase
      .from('waiting_room')
      .select('id')
      .eq('phone_number', realPhoneNumber)
      .single();

    if (waitingError && waitingError.code !== 'PGRST116') {
      throw waitingError;
    }

    if (waitingRoom) {
      console.log('⏳ User is in waiting room:', waitingRoom);
      return;
    }

    console.log('🆕 New user - not invited, not in waiting room');
  });

  test('should complete signup for +14167090286 with valid age', async () => {
    console.log('🎯 Testing complete signup for', realPhoneNumber);

    // First, verify there's an invite
    const { data: invite, error: inviteError } = await supabase
      .from('invites')
      .select('id, inviter_id, status')
      .eq('invitee_phone_number', realPhoneNumber)
      .eq('status', 'pending')
      .single();

    if (inviteError) {
      console.log('⚠️  No pending invite found, skipping signup test');
      return;
    }

    console.log('📨 Using invite:', invite);

    // Create user profile with valid age (25 years old)
    const validBirthDate = createTestDate(25);
    console.log('📅 Using birth date:', validBirthDate);

    const { data: newUser, error: signupError } = await supabase
      .from('user_profile')
      .insert({
        phone_number: realPhoneNumber,
        date_of_birth: validBirthDate,
        dob_verified: true,
      })
      .select('id, phone_number, date_of_birth, dob_verified, created_at')
      .single();

    if (signupError) {
      console.error('❌ Signup error:', signupError);
      throw signupError;
    }

    console.log('✅ User created successfully:', newUser);

    expect(newUser).toBeTruthy();
    expect(newUser.phone_number).toBe(realPhoneNumber);
    expect(newUser.dob_verified).toBe(true);
    expect(newUser.date_of_birth).toBe(validBirthDate);

    // Mark invite as accepted
    const { error: inviteUpdateError } = await supabase
      .from('invites')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      })
      .eq('id', invite.id);

    if (inviteUpdateError) {
      console.error('❌ Error updating invite:', inviteUpdateError);
      throw inviteUpdateError;
    }

    console.log('✅ Invite marked as accepted');

    // Verify invite was updated
    const { data: updatedInvite, error: verifyError } = await supabase
      .from('invites')
      .select('status, accepted_at')
      .eq('id', invite.id)
      .single();

    if (verifyError) {
      throw verifyError;
    }

    expect(updatedInvite.status).toBe('accepted');
    expect(updatedInvite.accepted_at).toBeTruthy();

    console.log('🎉 Complete signup flow successful!');
  });

  test('should reject signup for underage user', async () => {
    console.log('🚫 Testing underage rejection');

    // Clean up any existing user first
    await supabase.from('user_profile').delete().eq('phone_number', realPhoneNumber);

    const underageBirthDate = createTestDate(16); // 16 years old
    console.log('📅 Using underage birth date:', underageBirthDate);

    // Calculate age to verify it's under 18
    const birthDate = new Date(underageBirthDate);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('👶 Calculated age:', age);
    expect(age).toBeLessThan(18);

    // In a real edge function, this would be rejected
    // For this test, we'll simulate the age check
    if (age < 18) {
      console.log('✅ Age check correctly identifies underage user');
      expect(age).toBeLessThan(18);
    } else {
      fail('Age calculation error - user should be underage');
    }
  });

  test('should show current database state for +14167090286', async () => {
    console.log('📊 Current database state for', realPhoneNumber);

    // Check user_profile
    const { data: userProfile, error: userError } = await supabase
      .from('user_profile')
      .select('*')
      .eq('phone_number', realPhoneNumber)
      .single();

    if (userError && userError.code !== 'PGRST116') {
      throw userError;
    }

    console.log('👤 User Profile:', userProfile || 'Not found');

    // Check invites
    const { data: invites, error: inviteError } = await supabase
      .from('invites')
      .select('*')
      .eq('invitee_phone_number', realPhoneNumber);

    if (inviteError) {
      throw inviteError;
    }

    console.log('📨 Invites:', invites);

    // Check waiting room
    const { data: waitingRoom, error: waitingError } = await supabase
      .from('waiting_room')
      .select('*')
      .eq('phone_number', realPhoneNumber)
      .single();

    if (waitingError && waitingError.code !== 'PGRST116') {
      throw waitingError;
    }

    console.log('⏳ Waiting Room:', waitingRoom || 'Not found');

    // This test always passes - it's just for information
    expect(true).toBe(true);
  });
});
