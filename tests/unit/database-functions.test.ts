import { supabase, generateTestPhoneNumber, createTestDate } from '../setup';

describe('Database Functions Unit Tests', () => {
  describe('can_invite_more function', () => {
    let testUserId: string;

    beforeEach(async () => {
      // Create a test user
      const { data: testUser, error: userError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: generateTestPhoneNumber(),
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(userError).toBeNull();
      testUserId = testUser.id;
    });

    afterEach(async () => {
      // Clean up test data
      await supabase.from('invites').delete().eq('inviter_id', testUserId);
      await supabase.from('user_profile').delete().eq('id', testUserId);
    });

    test('should return true when user has no accepted invites', async () => {
      const { data, error } = await supabase
        .rpc('can_invite_more', { inviter_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(true);
    });

    test('should return true when user has less than 5 accepted invites', async () => {
      // Create 3 accepted invites
      const invites = Array.from({ length: 3 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(i.toString()),
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      }));

      const { error: inviteError } = await supabase
        .from('invites')
        .insert(invites);

      expect(inviteError).toBeNull();

      const { data, error } = await supabase
        .rpc('can_invite_more', { inviter_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(true);
    });

    test('should return false when user has 5 accepted invites', async () => {
      // Create 5 accepted invites
      const invites = Array.from({ length: 5 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(i.toString()),
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      }));

      const { error: inviteError } = await supabase
        .from('invites')
        .insert(invites);

      expect(inviteError).toBeNull();

      const { data, error } = await supabase
        .rpc('can_invite_more', { inviter_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(false);
    });

    test('should not count pending invites towards limit', async () => {
      // Create 3 accepted and 3 pending invites
      const acceptedInvites = Array.from({ length: 3 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(`acc${i}`),
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      }));

      const pendingInvites = Array.from({ length: 3 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(`pen${i}`),
        status: 'pending',
      }));

      const { error: acceptedError } = await supabase
        .from('invites')
        .insert(acceptedInvites);

      const { error: pendingError } = await supabase
        .from('invites')
        .insert(pendingInvites);

      expect(acceptedError).toBeNull();
      expect(pendingError).toBeNull();

      const { data, error } = await supabase
        .rpc('can_invite_more', { inviter_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(true); // Should still be true because only 3 accepted
    });
  });

  describe('get_available_invites function', () => {
    let testUserId: string;

    beforeEach(async () => {
      // Create a test user
      const { data: testUser, error: userError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: generateTestPhoneNumber(),
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(userError).toBeNull();
      testUserId = testUser.id;
    });

    afterEach(async () => {
      // Clean up test data
      await supabase.from('invites').delete().eq('inviter_id', testUserId);
      await supabase.from('user_profile').delete().eq('id', testUserId);
    });

    test('should return 5 when user has no accepted invites', async () => {
      const { data, error } = await supabase
        .rpc('get_available_invites', { user_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(5);
    });

    test('should return correct remaining invites', async () => {
      // Create 2 accepted invites
      const invites = Array.from({ length: 2 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(i.toString()),
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      }));

      const { error: inviteError } = await supabase
        .from('invites')
        .insert(invites);

      expect(inviteError).toBeNull();

      const { data, error } = await supabase
        .rpc('get_available_invites', { user_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(3); // 5 - 2 = 3
    });

    test('should return 0 when user has used all invites', async () => {
      // Create 5 accepted invites
      const invites = Array.from({ length: 5 }, (_, i) => ({
        inviter_id: testUserId,
        invitee_phone_number: generateTestPhoneNumber(i.toString()),
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      }));

      const { error: inviteError } = await supabase
        .from('invites')
        .insert(invites);

      expect(inviteError).toBeNull();

      const { data, error } = await supabase
        .rpc('get_available_invites', { user_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toBe(0);
    });
  });

  describe('Database Constraints and Indexes', () => {
    test('should enforce unique phone numbers in user_profile', async () => {
      const phoneNumber = generateTestPhoneNumber();

      // Create first user
      const { data: firstUser, error: firstError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: phoneNumber,
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(firstError).toBeNull();
      expect(firstUser).toBeTruthy();

      // Attempt to create second user with same phone number
      const { data: secondUser, error: secondError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: phoneNumber,
          date_of_birth: createTestDate(30),
          dob_verified: true,
        })
        .select()
        .single();

      expect(secondError).toBeTruthy();
      expect(secondError?.code).toBe('23505'); // Unique violation
      expect(secondUser).toBeNull();

      // Clean up
      await supabase.from('user_profile').delete().eq('id', firstUser.id);
    });

    test('should enforce unique phone numbers in waiting_room', async () => {
      const phoneNumber = generateTestPhoneNumber();

      // Add first entry
      const { data: firstEntry, error: firstError } = await supabase
        .from('waiting_room')
        .insert({ phone_number: phoneNumber })
        .select()
        .single();

      expect(firstError).toBeNull();
      expect(firstEntry).toBeTruthy();

      // Attempt to add duplicate entry
      const { data: secondEntry, error: secondError } = await supabase
        .from('waiting_room')
        .insert({ phone_number: phoneNumber })
        .select()
        .single();

      expect(secondError).toBeTruthy();
      expect(secondError?.code).toBe('23505'); // Unique violation
      expect(secondEntry).toBeNull();

      // Clean up
      await supabase.from('waiting_room').delete().eq('id', firstEntry.id);
    });

    test('should enforce unique inviter-invitee pairs', async () => {
      const inviterPhone = generateTestPhoneNumber('inv');
      const inviteePhone = generateTestPhoneNumber('invitee');

      // Create inviter user
      const { data: inviterUser, error: inviterError } = await supabase
        .from('user_profile')
        .insert({
          phone_number: inviterPhone,
          date_of_birth: createTestDate(25),
          dob_verified: true,
        })
        .select()
        .single();

      expect(inviterError).toBeNull();

      // Create first invite
      const { data: firstInvite, error: firstError } = await supabase
        .from('invites')
        .insert({
          inviter_id: inviterUser.id,
          invitee_phone_number: inviteePhone,
          status: 'pending',
        })
        .select()
        .single();

      expect(firstError).toBeNull();
      expect(firstInvite).toBeTruthy();

      // Attempt to create duplicate invite
      const { data: secondInvite, error: secondError } = await supabase
        .from('invites')
        .insert({
          inviter_id: inviterUser.id,
          invitee_phone_number: inviteePhone,
          status: 'pending',
        })
        .select()
        .single();

      expect(secondError).toBeTruthy();
      expect(secondError?.code).toBe('23505'); // Unique violation
      expect(secondInvite).toBeNull();

      // Clean up
      await supabase.from('invites').delete().eq('id', firstInvite.id);
      await supabase.from('user_profile').delete().eq('id', inviterUser.id);
    });
  });
});
