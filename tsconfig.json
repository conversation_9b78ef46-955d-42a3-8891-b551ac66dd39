{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "types": ["jest", "node"]}, "include": ["tests/**/*", "scripts/**/*", "supabase/functions/**/*"], "exclude": ["node_modules", "dist"]}